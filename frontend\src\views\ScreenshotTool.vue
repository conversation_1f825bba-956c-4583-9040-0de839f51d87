<template>
  <div class="screenshot-tool">
    <!-- 工具头部 -->
    <div class="tool-header">
      <div class="header-left">
        <h2>游戏截图数据收集工具</h2>
        <el-tag 
          :type="connectionStatus === 'connected' ? 'success' : 'danger'"
          size="small"
        >
          {{ connectionStatus === 'connected' ? '已连接' : '未连接' }}
        </el-tag>
      </div>
      <div class="header-right">
        <el-button-group>
          <el-button @click="refreshAll" :loading="globalLoading">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
          <el-button @click="showSettings = true">
            <el-icon><Setting /></el-icon>
            设置
          </el-button>
        </el-button-group>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="tool-content">
      <!-- 左侧控制面板 -->
      <div class="left-panel">
        <ControlPanel 
          :config="screenshotConfig"
          :loading="captureLoading"
          :stats="screenshotStats"
          @config-change="handleConfigChange"
          @capture="handleCapture"
          @start-preview="handleStartPreview"
          @stop-preview="handleStopPreview"
        />
      </div>

      <!-- 中间预览区域 -->
      <div class="center-panel">
        <PreviewArea
          :preview-active="previewActive"
          :preview-image="previewImage"
          :selected-region="selectedRegion"
          :loading="previewLoading"
          @region-select="handleRegionSelect"
          @refresh-preview="handleRefreshPreview"
        />
      </div>

      <!-- 右侧历史面板 -->
      <div class="right-panel">
        <HistoryPanel
          :history="screenshotHistory"
          :loading="historyLoading"
          @delete="handleDelete"
          @download="handleDownload"
          @refresh="refreshHistory"
          @batch-delete="handleBatchDelete"
        />
      </div>
    </div>

    <!-- 设置对话框 -->
    <el-dialog
      v-model="showSettings"
      title="截图工具设置"
      width="600px"
    >
      <SettingsPanel
        :config="toolConfig"
        @save="handleSaveSettings"
      />
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Refresh, Setting } from '@element-plus/icons-vue'
import ControlPanel from '../components/screenshot/ControlPanel.vue'
import PreviewArea from '../components/screenshot/PreviewArea.vue'
import HistoryPanel from '../components/screenshot/HistoryPanel.vue'
import SettingsPanel from '../components/screenshot/SettingsPanel.vue'
import { useScreenshotApi } from '../composables/useScreenshotApi'
import { useWebSocket } from '../composables/useWebSocket'

// 响应式数据
const connectionStatus = ref('disconnected')
const globalLoading = ref(false)
const captureLoading = ref(false)
const previewLoading = ref(false)
const historyLoading = ref(false)
const showSettings = ref(false)

// 预览相关
const previewActive = ref(false)
const previewImage = ref(null)
const selectedRegion = ref(null)

// 截图配置
const screenshotConfig = reactive({
  mode: 'window',
  format: 'png',
  quality: 90,
  region: null,
  save_to_disk: true,
  filename_prefix: 'screenshot'
})

// 工具配置
const toolConfig = reactive({
  game_window: {
    window_title: 'gakumas',
    enable_background_mode: true,
    capture_method: 'auto'
  },
  storage: {
    base_directory: 'screenshots',
    max_history_size: 1000,
    auto_cleanup: true,
    cleanup_days: 30
  },
  preview_fps: 2,
  default_quality: 90,
  default_format: 'png'
})

// 历史记录和统计
const screenshotHistory = ref([])
const screenshotStats = ref({})

// API 和 WebSocket
const { 
  captureScreenshot, 
  getScreenshotHistory, 
  deleteScreenshot,
  batchDeleteScreenshots,
  downloadScreenshot,
  getScreenshotStats
} = useScreenshotApi()

const { 
  connect, 
  disconnect, 
  sendMessage, 
  onMessage 
} = useWebSocket()

// 生命周期
onMounted(async () => {
  await initializeApp()
})

onUnmounted(() => {
  disconnect()
  if (previewActive.value) {
    handleStopPreview()
  }
})

// 方法
async function initializeApp() {
  try {
    globalLoading.value = true
    
    // 连接 WebSocket
    await connect()
    connectionStatus.value = 'connected'
    
    // 设置消息处理
    setupWebSocketHandlers()
    
    // 加载初始数据
    await Promise.all([
      refreshHistory(),
      refreshStats()
    ])
    
    ElMessage.success('截图工具初始化完成')
    
  } catch (error) {
    ElMessage.error('初始化失败: ' + error.message)
    connectionStatus.value = 'disconnected'
  } finally {
    globalLoading.value = false
  }
}

function setupWebSocketHandlers() {
  // 截图任务状态更新
  onMessage('screenshot_task_started', (data) => {
    ElMessage.info('截图任务已开始')
    captureLoading.value = true
  })
  
  onMessage('screenshot_task_completed', (data) => {
    ElMessage.success('截图完成')
    captureLoading.value = false
    refreshHistory()
    refreshStats()
  })
  
  onMessage('screenshot_task_failed', (data) => {
    ElMessage.error('截图失败: ' + data.error)
    captureLoading.value = false
  })
  
  // 预览流处理
  onMessage('screenshot_preview', (data) => {
    previewImage.value = 'data:image/jpeg;base64,' + data.image_data
  })
  
  // 连接状态处理
  onMessage('connection_established', () => {
    connectionStatus.value = 'connected'
  })
}

async function handleCapture() {
  try {
    captureLoading.value = true
    
    const result = await captureScreenshot({
      config: screenshotConfig
    })
    
    if (result.success) {
      ElMessage.success(`截图已保存: ${result.filename}`)
      await refreshHistory()
      await refreshStats()
    } else {
      ElMessage.error('截图失败: ' + result.error_message)
    }
    
  } catch (error) {
    ElMessage.error('截图失败: ' + error.message)
  } finally {
    captureLoading.value = false
  }
}

function handleConfigChange(newConfig) {
  Object.assign(screenshotConfig, newConfig)
}

function handleRegionSelect(region) {
  selectedRegion.value = region
  screenshotConfig.region = region
}

async function handleStartPreview() {
  try {
    previewLoading.value = true
    previewActive.value = true
    
    await sendMessage({
      type: 'start_preview'
    })
    
    ElMessage.success('预览已启动')
    
  } catch (error) {
    ElMessage.error('启动预览失败: ' + error.message)
    previewActive.value = false
  } finally {
    previewLoading.value = false
  }
}

async function handleStopPreview() {
  try {
    previewActive.value = false
    previewImage.value = null
    
    await sendMessage({
      type: 'stop_preview'
    })
    
    ElMessage.info('预览已停止')
    
  } catch (error) {
    ElMessage.error('停止预览失败: ' + error.message)
  }
}

async function handleRefreshPreview() {
  if (!previewActive.value) {
    try {
      previewLoading.value = true
      
      // 获取单帧预览
      const response = await fetch('/api/v1/screenshot/preview')
      if (response.ok) {
        const blob = await response.blob()
        const imageUrl = URL.createObjectURL(blob)
        previewImage.value = imageUrl
      }
      
    } catch (error) {
      ElMessage.error('刷新预览失败: ' + error.message)
    } finally {
      previewLoading.value = false
    }
  }
}

async function refreshHistory() {
  try {
    historyLoading.value = true
    const response = await getScreenshotHistory(100)
    screenshotHistory.value = response.records || []
  } catch (error) {
    ElMessage.error('获取历史记录失败: ' + error.message)
  } finally {
    historyLoading.value = false
  }
}

async function refreshStats() {
  try {
    const stats = await getScreenshotStats()
    screenshotStats.value = stats
  } catch (error) {
    console.error('获取统计信息失败:', error)
  }
}

async function handleDelete(screenshotId) {
  try {
    await ElMessageBox.confirm('确定要删除这张截图吗？', '确认删除', {
      type: 'warning'
    })
    
    const success = await deleteScreenshot(screenshotId)
    if (success) {
      ElMessage.success('截图已删除')
      await refreshHistory()
      await refreshStats()
    }
    
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败: ' + error.message)
    }
  }
}

async function handleBatchDelete(screenshotIds) {
  try {
    await ElMessageBox.confirm(`确定要删除选中的 ${screenshotIds.length} 张截图吗？`, '批量删除', {
      type: 'warning'
    })
    
    const result = await batchDeleteScreenshots(screenshotIds)
    
    if (result.success_count > 0) {
      ElMessage.success(`成功删除 ${result.success_count} 张截图`)
    }
    
    if (result.failed_count > 0) {
      ElMessage.warning(`${result.failed_count} 张截图删除失败`)
    }
    
    await refreshHistory()
    await refreshStats()
    
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量删除失败: ' + error.message)
    }
  }
}

async function handleDownload(screenshotId) {
  try {
    await downloadScreenshot(screenshotId)
    ElMessage.success('下载完成')
  } catch (error) {
    ElMessage.error('下载失败: ' + error.message)
  }
}

async function handleSaveSettings(newConfig) {
  try {
    Object.assign(toolConfig, newConfig)
    showSettings.value = false
    ElMessage.success('设置已保存')
  } catch (error) {
    ElMessage.error('保存设置失败: ' + error.message)
  }
}

async function refreshAll() {
  await Promise.all([
    refreshHistory(),
    refreshStats()
  ])
  ElMessage.success('数据已刷新')
}
</script>

<style scoped>
.screenshot-tool {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f7fa;
}

.tool-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  background: white;
  border-bottom: 1px solid #e6e6e6;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.header-left h2 {
  margin: 0;
  color: #409eff;
  font-size: 20px;
  font-weight: 600;
}

.tool-content {
  flex: 1;
  display: flex;
  gap: 16px;
  padding: 16px;
  min-height: 0;
}

.left-panel {
  width: 320px;
  flex-shrink: 0;
}

.center-panel {
  flex: 1;
  min-width: 0;
}

.right-panel {
  width: 320px;
  flex-shrink: 0;
}

@media (max-width: 1200px) {
  .tool-content {
    flex-direction: column;
  }
  
  .left-panel,
  .right-panel {
    width: 100%;
  }
}
</style>
