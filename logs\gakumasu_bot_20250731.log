2025-07-31 08:36:46 - GakumasuBot - INFO - logger.py:109 - 日志系统初始化完成 - 级别: INFO
2025-07-31 08:36:46 - GakumasuBot - INFO - gui.py:76 - GUI启动器初始化完成
2025-07-31 08:36:46 - GakumasuBot - INFO - gui.py:235 - 启动模式: development
2025-07-31 08:36:46 - GakumasuBot - INFO - gui.py:106 - 后端端口 8000 可用
2025-07-31 08:36:46 - GakumasuBot - INFO - gui.py:116 - 前端端口 3000 可用
2025-07-31 08:36:46 - GakumasuBot - INFO - gui.py:130 - 启动后端服务: uvicorn src.web.main:app --host 127.0.0.1 --port 8000 --reload
2025-07-31 08:36:46 - GakumasuBot - INFO - gui.py:185 - 等待服务就绪: http://127.0.0.1:8000/health
2025-07-31 08:36:46 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     Will watch for changes in these directories: ['J:\\笔记本专用\\代码\\Gakumasu-Bot']
2025-07-31 08:36:46 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     Uvicorn running on http://127.0.0.1:8000 (Press CTRL+C to quit)
2025-07-31 08:36:46 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     Started reloader process [3036] using StatReload
2025-07-31 08:36:50 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     Started server process [24796]
2025-07-31 08:36:50 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     Waiting for application startup.
2025-07-31 08:36:50 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     Application startup complete.
2025-07-31 08:36:51 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:64188 - "GET /health HTTP/1.1" 200 OK
2025-07-31 08:36:51 - GakumasuBot - INFO - gui.py:192 - 服务就绪: http://127.0.0.1:8000/health
2025-07-31 08:36:51 - GakumasuBot - INFO - gui.py:166 - 启动前端服务: npm run dev
2025-07-31 08:36:51 - GakumasuBot - INFO - gui.py:185 - 等待服务就绪: http://127.0.0.1:3000
2025-07-31 08:36:53 - GakumasuBot - INFO - gui.py:192 - 服务就绪: http://127.0.0.1:3000
2025-07-31 08:36:53 - GakumasuBot - INFO - gui.py:260 - 所有服务启动完成，按 Ctrl+C 退出
