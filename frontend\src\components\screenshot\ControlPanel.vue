<template>
  <div class="control-panel">
    <!-- 截图模式选择 -->
    <el-card header="截图模式" class="panel-card">
      <el-radio-group 
        :model-value="config.mode" 
        @update:model-value="updateConfig('mode', $event)"
        class="mode-selector"
      >
        <el-radio value="fullscreen" class="mode-option">
          <div class="mode-content">
            <el-icon><FullScreen /></el-icon>
            <span>全屏截图</span>
          </div>
        </el-radio>
        <el-radio value="window" class="mode-option">
          <div class="mode-content">
            <el-icon><Monitor /></el-icon>
            <span>窗口截图</span>
          </div>
        </el-radio>
        <el-radio value="region" class="mode-option">
          <div class="mode-content">
            <el-icon><Crop /></el-icon>
            <span>区域截图</span>
          </div>
        </el-radio>
      </el-radio-group>
    </el-card>

    <!-- 截图设置 -->
    <el-card header="截图设置" class="panel-card">
      <el-form label-width="80px" size="small">
        <el-form-item label="图片格式">
          <el-select 
            :model-value="config.format" 
            @update:model-value="updateConfig('format', $event)"
            style="width: 100%"
          >
            <el-option label="PNG" value="png" />
            <el-option label="JPEG" value="jpeg" />
            <el-option label="BMP" value="bmp" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="图片质量">
          <el-slider 
            :model-value="config.quality" 
            @update:model-value="updateConfig('quality', $event)"
            :min="10" 
            :max="100" 
            show-input
            :disabled="config.format === 'png'"
          />
          <div class="quality-hint" v-if="config.format === 'png'">
            PNG格式无损压缩，质量设置无效
          </div>
        </el-form-item>
        
        <el-form-item label="文件前缀">
          <el-input 
            :model-value="config.filename_prefix" 
            @update:model-value="updateConfig('filename_prefix', $event)"
            placeholder="screenshot"
          />
        </el-form-item>
        
        <el-form-item>
          <el-checkbox 
            :model-value="config.save_to_disk" 
            @update:model-value="updateConfig('save_to_disk', $event)"
          >
            保存到磁盘
          </el-checkbox>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 快速操作 -->
    <el-card header="快速操作" class="panel-card">
      <div class="quick-actions">
        <el-button 
          type="primary" 
          :loading="loading"
          @click="$emit('capture')"
          size="large"
          class="action-button"
        >
          <el-icon><Camera /></el-icon>
          立即截图
        </el-button>
        
        <el-button 
          type="success" 
          @click="$emit('start-preview')"
          size="large"
          class="action-button"
        >
          <el-icon><VideoPlay /></el-icon>
          开始预览
        </el-button>
        
        <el-button 
          type="warning" 
          @click="$emit('stop-preview')"
          size="large"
          class="action-button"
        >
          <el-icon><VideoPause /></el-icon>
          停止预览
        </el-button>
      </div>
    </el-card>

    <!-- 统计信息 -->
    <el-card header="统计信息" class="panel-card">
      <div class="stats-grid">
        <div class="stat-item">
          <div class="stat-label">总截图数</div>
          <div class="stat-value">{{ stats.total_screenshots || 0 }}</div>
        </div>
        <div class="stat-item">
          <div class="stat-label">存储大小</div>
          <div class="stat-value">{{ formatFileSize(stats.total_size_bytes || 0) }}</div>
        </div>
        <div class="stat-item">
          <div class="stat-label">预览状态</div>
          <div class="stat-value">
            <el-tag :type="stats.preview_active ? 'success' : 'info'" size="small">
              {{ stats.preview_active ? '活动' : '停止' }}
            </el-tag>
          </div>
        </div>
        <div class="stat-item">
          <div class="stat-label">存储目录</div>
          <div class="stat-value stat-path">{{ stats.storage_directory || '-' }}</div>
        </div>
      </div>
    </el-card>

    <!-- 区域选择提示 -->
    <el-card v-if="config.mode === 'region'" header="区域选择" class="panel-card">
      <el-alert
        title="区域截图模式"
        type="info"
        :closable="false"
        show-icon
      >
        <template #default>
          在预览区域中拖拽鼠标选择截图区域，选择完成后点击"立即截图"按钮。
        </template>
      </el-alert>
    </el-card>
  </div>
</template>

<script setup>
import { 
  FullScreen, 
  Monitor, 
  Crop, 
  Camera, 
  VideoPlay, 
  VideoPause 
} from '@element-plus/icons-vue'

// Props
const props = defineProps({
  config: {
    type: Object,
    required: true
  },
  loading: {
    type: Boolean,
    default: false
  },
  stats: {
    type: Object,
    default: () => ({})
  }
})

// Emits
const emit = defineEmits([
  'config-change',
  'capture',
  'start-preview',
  'stop-preview'
])

// 方法
function updateConfig(key, value) {
  const newConfig = { ...props.config, [key]: value }
  emit('config-change', newConfig)
}

function formatFileSize(bytes) {
  if (bytes === 0) return '0 B'
  
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}
</script>

<style scoped>
.control-panel {
  display: flex;
  flex-direction: column;
  gap: 16px;
  height: 100%;
}

.panel-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.panel-card :deep(.el-card__header) {
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  font-weight: 600;
  color: #495057;
}

.mode-selector {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.mode-option {
  width: 100%;
  margin: 0;
  padding: 12px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.mode-option:hover {
  border-color: #409eff;
  background: #f0f8ff;
}

.mode-option.is-checked {
  border-color: #409eff;
  background: #e6f3ff;
}

.mode-content {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
}

.mode-content .el-icon {
  font-size: 18px;
  color: #409eff;
}

.quality-hint {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.quick-actions {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.action-button {
  width: 100%;
  height: 48px;
  font-size: 16px;
  font-weight: 600;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.stat-item {
  text-align: center;
}

.stat-label {
  font-size: 12px;
  color: #909399;
  margin-bottom: 4px;
}

.stat-value {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.stat-path {
  font-size: 12px;
  word-break: break-all;
  grid-column: 1 / -1;
}

.el-form-item {
  margin-bottom: 16px;
}

.el-alert {
  border-radius: 6px;
}
</style>
